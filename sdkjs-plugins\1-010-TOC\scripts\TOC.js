/**
 * OnlyOffice目录生成插件
 * 功能：根据文档标题自动生成目录，支持多级标题和特殊章节
 */
(function (window, undefined) {
  // 全局变量：记录用户选择的目录级别
  var checkedValue = 1;

  /**
   * 初始化插件UI交互
   * 设置复选框的级联选择逻辑
   */
  function initPlugin() {
    // 为所有标题级别复选框添加事件监听
    document.querySelectorAll('.title-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', function () {
        if (this.checked) {
          // 获取当前复选框的索引
          const currentIndex = parseInt(this.getAttribute('data-index'));
          checkedValue = currentIndex;

          // 级联选择：选中当前级别时，自动选中前面所有级别
          for (let i = 1; i < currentIndex; i++) {
            const prevCheckbox = document.querySelector(`.title-checkbox[data-index="${i}"]`);
            prevCheckbox.checked = true;
          }
        } else {
          // 级联取消：取消当前级别时，自动取消后面所有级别
          const currentIndex = parseInt(this.getAttribute('data-index'));
          checkedValue = currentIndex - 1;
          for (let i = currentIndex + 1; i <= 5; i++) {
            const nextCheckbox = document.querySelector(`.title-checkbox[data-index="${i}"]`);
            nextCheckbox.checked = false;
          }
        }
      });
    });
  }

  /**
   * 生成目录的主函数
   * 执行目录生成逻辑，包括查找标题、计算页码、插入目录项
   */
  function setTOC() {
    // 验证用户是否选择了目录级别
    if (checkedValue == 0) {
      window.Asc.plugin.executeMethod('ShowError', ['所选目次不能为空']);
      return;
    }

    // 设置目录包含的大纲级别
    Asc.scope.outlineLvls = checkedValue + 1;

    // 调用OnlyOffice API执行文档操作
    window.Asc.plugin.callCommand(
      function () {
        // 获取文档对象和所有段落
        let oDocument = Api.GetDocument();
        let allParagraphs = oDocument.GetAllParagraphs();
        let allHeadingParagraphs = oDocument.GetAllHeadingParagraphs();

        // 初始化关键位置变量
        var tocIndex = -1;          // 目录标题的段落索引
        var tocParagraph = null;    // 目录标题段落对象
        var forewordPage = -1;      // 前言页码
        var introductionPage = -1;  // 引言页码
        var standardPage = -1;      // 正文开始页码
        var contentStartIndex = -1; // 内容开始的段落索引
        var indexes = -1;           // 索引段落索引

        /**
         * 罗马数字转阿拉伯数字
         * 用于处理前言、引言等使用罗马数字页码的章节
         * @param {string} num - 罗马数字字符串
         * @returns {number} - 对应的阿拉伯数字
         */
        function romanToInt(num) {
          const roman = { I: 1, V: 5, X: 10, L: 50 };
          let result = 0;

          for (let i = 0; i < num.length; i++) {
            const currentVal = roman[num[i]];
            const nextVal = roman[num[i + 1]];

            if (nextVal > currentVal) {
              result += nextVal - currentVal;
              i++;
            } else {
              result += currentVal;
            }
          }
          return result;
        }

        // 遍历所有段落，查找关键章节和位置信息
        for (var i = 0; i < allParagraphs.length; i++) {
          var paragraph = allParagraphs[i];
          var text = paragraph.GetText().trim();
          var style = paragraph?.GetStyle()?.GetName();

          // 查找目录标题位置
          if (text === '目次' && style === '标准文件_目录标题') {
            tocIndex = i;
            tocParagraph = paragraph;
          }

          // 查找前言页码（使用罗马数字）
          if (text === '前言' && style === '标准文件_前言、引言标题')
            forewordPage = romanToInt(paragraph?.GetRange()?.GetStartPage());

          // 查找引言页码（使用罗马数字）
          if (text === '引言' && style === '标准文件_前言、引言标题')
            introductionPage = romanToInt(paragraph?.GetRange()?.GetStartPage());

          // 查找正文开始页码（阿拉伯数字的起始点）
          if (style === '标准文件_正文标准名称') standardPage = paragraph?.GetRange()?.GetStartPage();

          // 确定内容开始的段落索引
          if (contentStartIndex === -1 && (forewordPage != -1 || introductionPage != -1 || standardPage != -1))
            contentStartIndex = i;

          // 查找索引位置
          if (text === '索引' && style === '标准文件_索引标题') indexes = i;

          // 找到索引后可以停止遍历
          if (indexes !== -1) break;
        }

        // 验证是否找到了正文内容
        if (contentStartIndex === -1) {
          return { error: '未找到正文标题' };
        }

        // 清理目录和内容开始之间的多余段落
        if (tocIndex < contentStartIndex) {
          for (var i = contentStartIndex - 1; i > tocIndex; i--) {
            if (allParagraphs[i].GetText().trim() != '') allParagraphs[i].Delete();
          }
        }

        /**
         * 阿拉伯数字转罗马数字
         * 用于为前言、引言等章节生成罗马数字页码
         * @param {number} num - 阿拉伯数字
         * @returns {string} - 对应的罗马数字字符串
         */
        function intToRoman(num) {
          const val = [50, 40, 10, 9, 5, 4, 1];
          const sym = ['L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
          let roman = '';

          for (let i = 0; i < val.length; i++) {
            while (num >= val[i]) {
              roman += sym[i];
              num -= val[i];
            }
          }

          return roman;
        }

        // 收集所有需要包含在目录中的标题段落
        let nParagraphs = [];
        allHeadingParagraphs.forEach(paragraph => {
          let text = paragraph.GetText().trim();
          let paragraphStyle = paragraph?.GetStyle()?.GetName();
          let oParagraphLevelIndex = paragraph?.GetNumbering()?.GetLevelIndex();

          // 根据段落级别确定目录样式
          let indexesStyle = 'toc ' + (!oParagraphLevelIndex || Number(oParagraphLevelIndex) <= 1 ? '1' : oParagraphLevelIndex);
          let currentPage = paragraph?.GetRange()?.GetStartPage() || 0;

          // 检查页码是否为罗马数字格式
          const romanRegex = /^[IVXLCDM]+$/i;
          currentPage = romanRegex.test(currentPage) ? romanToInt(paragraph?.GetRange()?.GetStartPage()) : currentPage;

          // 根据页码位置决定使用罗马数字还是阿拉伯数字
          if (currentPage < standardPage) {
            currentPage = intToRoman(currentPage);  // 前言、引言使用罗马数字
          } else {
            currentPage = currentPage - standardPage + 1;  // 正文使用阿拉伯数字
          }

          // 判断是否应该包含在目录中
          let shouldInclude = false;

          // 如果有编号级别且在用户选择的范围内，包含该标题
          if (oParagraphLevelIndex && oParagraphLevelIndex <= Asc.scope.outlineLvls) {
            shouldInclude = true;
          }

          // 特殊处理：参考文献和索引总是包含在目录中
          if (paragraphStyle === '标准文件_参考文献标题' || paragraphStyle === '标准文件_索引标题') {
            shouldInclude = true;
            indexesStyle = 'toc 1';
          }

          // 将符合条件的段落添加到目录数组中
          if (shouldInclude) {
            nParagraphs.unshift({
              currentPage: currentPage.toString(),
              text: text,
              oParagraphLevelIndex: oParagraphLevelIndex,
              indexesStyle: indexesStyle,
              paragraphStyle: paragraphStyle,
            });
          }
        });

        // 如果找到了目录项，则生成目录
        if (nParagraphs.length > 0) {
          // 遍历所有目录项，创建目录段落
          nParagraphs.forEach(item => {
            // 创建新的目录段落
            var nParagraph = Api.CreateParagraph();
            // 添加标题文本
            nParagraph.AddText(item.text);
            // 设置目录样式
            nParagraph.SetStyle(oDocument.GetStyle(item.indexesStyle));
            // 添加制表符（用于对齐页码）
            nParagraph.AddTabStop();
            // 添加页码
            nParagraph.AddText(item.currentPage || '0');
            // 将目录段落插入到目录标题后面
            tocParagraph.InsertParagraph(nParagraph, 'after');
          });
          // 选中目录标题段落
          allParagraphs[tocIndex].Select();
          return;
        } else {
          // 如果没有找到任何目录项，返回错误
          return { error: '未查找到目次内容' };
        }
      },
      false,
      true,
      // 回调函数：处理命令执行结果
      function (result) {
        // 如果执行过程中出现错误，显示错误信息
        if (result && result.error) {
          window.Asc.plugin.executeMethod('ShowError', [result.error]);
        }
        // 结束操作并保存
        window.Asc.plugin.executeMethod('EndAction', ['Block', 'Save to local storage...', '']);
        // 关闭插件窗口
        window.Asc.plugin.executeCommand('close', '');
      }
    );
  }

  /**
   * 插件初始化函数
   * OnlyOffice插件加载时自动调用
   */
  window.Asc.plugin.init = function () {
    initPlugin();
  };

  /**
   * 插件按钮点击事件处理函数
   * @param {string} id - 按钮ID，'1'表示确定按钮，其他表示取消按钮
   */
  window.Asc.plugin.button = function (id) {
    if (id == '1') {
      // 点击确定按钮，执行目录生成
      setTOC.call(this);
    } else {
      // 点击取消按钮，直接关闭插件
      window.Asc.plugin.executeCommand('close', '');
    }
  };
})(window, undefined);
